<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form người dùng - Spring Boot Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Spring Boot Demo</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">Trang chủ</a>
                <a class="nav-link" href="/users">Quản lý người dùng</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 th:text="${user.id != null ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới'}"></h4>
                    </div>
                    <div class="card-body">
                        <div th:if="${param.error}" class="alert alert-danger">
                            Email đã tồn tại hoặc có lỗi xảy ra!
                        </div>

                        <form th:action="${user.id != null ? '/users/update/' + user.id : '/users'}" 
                              th:object="${user}" method="post">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Tên *</label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" th:field="*{email}" required>
                            </div>

                            <div class="mb-3">
                                <label for="phone" class="form-label">Số điện thoại</label>
                                <input type="tel" class="form-control" id="phone" th:field="*{phone}">
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/users" class="btn btn-secondary me-md-2">Hủy</a>
                                <button type="submit" class="btn btn-primary" 
                                        th:text="${user.id != null ? 'Cập nhật' : 'Thêm mới'}"></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
