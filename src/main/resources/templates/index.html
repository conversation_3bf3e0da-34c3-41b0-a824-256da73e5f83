<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Boot Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Spring Boot Demo</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">Trang chủ</a>
                <a class="nav-link" href="/users">Quản lý người dùng</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="jumbotron bg-light p-5 rounded">
                    <h1 class="display-4">Chào mừng đến với Spring Boot Demo!</h1>
                    <p class="lead">Đây là một ứng dụng web đơn giản được xây dựng bằng Spring Boot.</p>
                    <hr class="my-4">
                    <p>Ứng dụng này bao gồm các tính năng cơ bản như quản lý người dùng với CRUD operations.</p>
                    <a class="btn btn-primary btn-lg" href="/users" role="button">Xem danh sách người dùng</a>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Tính năng</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>Quản lý người dùng (CRUD)</li>
                            <li>REST API endpoints</li>
                            <li>Giao diện web với Thymeleaf</li>
                            <li>Database H2 in-memory</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Thống kê</h5>
                    </div>
                    <div class="card-body">
                        <p>Tổng số người dùng: <span class="badge bg-primary" th:text="${#lists.size(users)}">0</span></p>
                        <a href="/users/new" class="btn btn-success">Thêm người dùng mới</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
