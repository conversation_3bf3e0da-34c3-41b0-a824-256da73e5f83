<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý người dùng - Spring Boot Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Spring Boot Demo</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">Trang chủ</a>
                <a class="nav-link active" href="/users">Quản lý người dùng</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>Danh sách người dùng</h2>
                    <a href="/users/new" class="btn btn-primary">Thêm người dùng mới</a>
                </div>

                <div th:if="${param.error}" class="alert alert-danger">
                    Có lỗi xảy ra khi thực hiện thao tác!
                </div>

                <div class="card">
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(users)}" class="text-center py-4">
                            <p class="text-muted">Chưa có người dùng nào. <a href="/users/new">Thêm người dùng đầu tiên</a></p>
                        </div>

                        <div th:unless="${#lists.isEmpty(users)}">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Tên</th>
                                        <th>Email</th>
                                        <th>Số điện thoại</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="user : ${users}">
                                        <td th:text="${user.id}"></td>
                                        <td th:text="${user.name}"></td>
                                        <td th:text="${user.email}"></td>
                                        <td th:text="${user.phone}"></td>
                                        <td>
                                            <a th:href="@{/users/edit/{id}(id=${user.id})}" class="btn btn-sm btn-outline-primary">Sửa</a>
                                            <a th:href="@{/users/delete/{id}(id=${user.id})}" 
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('Bạn có chắc chắn muốn xóa người dùng này?')">Xóa</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
