/* Custom styles for Spring Boot Demo */

body {
    background-color: #f8f9fa;
}

.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.navbar-brand {
    font-weight: bold;
}

.table th {
    border-top: none;
    background-color: #f8f9fa;
}

.btn {
    border-radius: 0.375rem;
}

.alert {
    border-radius: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
}
